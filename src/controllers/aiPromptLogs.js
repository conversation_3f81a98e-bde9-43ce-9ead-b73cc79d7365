import apiClient from '../lib/api_client.js';
import { 
  getRecentAiPromptLogs, 
  getAiPromptLogById, 
  getAiPromptLogsByDateRange,
  getTokenUsageStats 
} from '../db/queries/aiPromptLogs.js';

/**
 * Get recent AI prompt logs
 * GET /admin/ai-prompt-logs
 */
export const getAiPromptLogs = async (req, res, next) => {
  try {
    const { limit = 100, start_date, end_date } = req.query;

    let logs;
    if (start_date && end_date) {
      logs = await getAiPromptLogsByDateRange(new Date(start_date), new Date(end_date));
    } else {
      logs = await getRecentAiPromptLogs(parseInt(limit));
    }

    return apiClient.success(req, res, next, logs);
  } catch (error) {
    console.error('Error fetching AI prompt logs:', error);
    return apiClient.error(req, res, next, 'Failed to fetch AI prompt logs', 500);
  }
};

/**
 * Get specific AI prompt log by ID
 * GET /admin/ai-prompt-logs/:id
 */
export const getAiPromptLog = async (req, res, next) => {
  try {
    const { id } = req.params;
    const log = await getAiPromptLogById(parseInt(id));

    if (!log) {
      return apiClient.error(req, res, next, 'AI prompt log not found', 404);
    }

    return apiClient.success(req, res, next, log);
  } catch (error) {
    console.error('Error fetching AI prompt log:', error);
    return apiClient.error(req, res, next, 'Failed to fetch AI prompt log', 500);
  }
};

/**
 * Get token usage statistics
 * GET /admin/ai-prompt-logs/stats
 */
export const getAiPromptLogStats = async (req, res, next) => {
  try {
    const { start_date, end_date } = req.query;

    const stats = await getTokenUsageStats(
      start_date ? new Date(start_date) : null,
      end_date ? new Date(end_date) : null
    );

    return apiClient.success(req, res, next, stats);
  } catch (error) {
    console.error('Error fetching AI prompt log stats:', error);
    return apiClient.error(req, res, next, 'Failed to fetch AI prompt log stats', 500);
  }
};

const AI_PROMPT_LOGS_CONTROLLER = {
  getAiPromptLogs,
  getAiPromptLog,
  getAiPromptLogStats
};

export default AI_PROMPT_LOGS_CONTROLLER;
